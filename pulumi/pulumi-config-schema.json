{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "definitions": {"BackOfficeConfig": {"type": "object", "properties": {"appConfig": {"$ref": "#/definitions/AppConfig", "description": "Optional root-level application configuration that serves as a base. This is merged with component-specific configs (server.appConfig, worker.appConfig). Component configs take precedence over this base config when keys overlap."}, "server": {"$ref": "#/definitions/BackOfficeServerConfig", "description": "Server component configuration. Defines all settings for the API server component including image, port, resources, environment, secrets, and specific application config."}, "worker": {"$ref": "#/definitions/BackOfficeWorkerConfig", "description": "Worker component configuration. Defines all settings for the background worker component including image, resources, environment, secrets, and specific application config."}, "namespaces": {"type": "object", "properties": {"server": {"type": "string", "description": "Namespace for the server component. Default: \"${appName}-server\""}, "worker": {"type": "string", "description": "Namespace for the worker component. Default: \"${appName}-worker\""}}, "additionalProperties": false, "description": "Namespace configuration for server and worker components.\n- If specified, the components will use these existing namespaces\n- If not specified, default namespaces are derived from appName Note: Pulumi does NOT create these namespaces, they must exist in the Kubernetes cluster"}}, "required": ["server", "worker"], "additionalProperties": false, "description": "Top-level configuration for the BackOffice Pulumi component. Defines the complete configuration for both server and worker components, as well as shared namespaces and configuration."}, "AppConfig": {"type": "object", "properties": {"cms": {"$ref": "#/definitions/CmsClientConfig"}, "server": {"$ref": "#/definitions/ServerConfig"}, "temporal": {"$ref": "#/definitions/TemporalConfig"}, "task": {"$ref": "#/definitions/TaskConfig"}, "graph": {"$ref": "#/definitions/GraphConfig"}, "issue": {"$ref": "#/definitions/IssueConfig"}, "source": {"$ref": "#/definitions/SourceConfig"}, "etl": {"$ref": "#/definitions/EtlConfig"}}, "additionalProperties": false}, "CmsClientConfig": {"type": "object", "properties": {"baseUrl": {"type": "string"}, "apiKey": {"type": "string"}, "timeoutSeconds": {"type": "number"}}, "additionalProperties": false}, "ServerConfig": {"type": "object", "properties": {"address": {"type": "string"}, "port": {"type": "number"}, "middleware": {"$ref": "#/definitions/ServerMiddlewareConfig"}, "auth": {"$ref": "#/definitions/ServerAuthConfig"}}, "additionalProperties": false}, "ServerMiddlewareConfig": {"type": "object", "properties": {"requestTimeoutMs": {"type": "number"}, "openapi": {"$ref": "#/definitions/ServerMiddlewareOpenapiConfig"}, "logger": {"$ref": "#/definitions/ServerMiddlewareRequestLoggerConfig"}}, "additionalProperties": false}, "ServerMiddlewareOpenapiConfig": {"type": "object", "properties": {"validateRequests": {"type": "boolean"}, "validateResponses": {"type": "boolean"}}, "additionalProperties": false}, "ServerMiddlewareRequestLoggerConfig": {"type": "object", "properties": {"enabled": {"type": "boolean"}}, "additionalProperties": false}, "ServerAuthConfig": {"type": "object", "properties": {"jwksUrl": {"type": "string"}, "audience": {"type": "string"}, "issuer": {"type": "string"}, "rolesClaimName": {"type": "string"}, "rolesDomain": {"type": "string"}, "rolePermissions": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}, "additionalProperties": false}, "TemporalConfig": {"type": "object", "properties": {"client": {"$ref": "#/definitions/TemporalClientConfig"}, "worker": {"$ref": "#/definitions/TemporalWorkerOptions"}, "task": {"$ref": "#/definitions/TemporalTaskConfig"}}, "additionalProperties": false}, "TemporalClientConfig": {"type": "object", "properties": {"hostPort": {"type": "string"}, "namespace": {"type": "string"}, "taskQueue": {"type": "string"}}, "additionalProperties": false}, "TemporalWorkerOptions": {"type": "object", "properties": {"maxConcurrentActivityExecutionSize": {"type": "number"}, "workerActivitiesPerSecond": {"type": "number"}, "maxConcurrentWorkflowTaskExecutionSize": {"type": "number"}, "identity": {"type": "string"}}, "additionalProperties": false}, "TemporalTaskConfig": {"type": "object", "properties": {"fastActivityTimeoutSeconds": {"type": "number"}, "slowActivityTimeoutSeconds": {"type": "number"}, "taskCreationWaitTimeoutSeconds": {"type": "number"}, "taskCreationPollIntervalMilliseconds": {"type": "number"}}, "additionalProperties": false}, "TaskConfig": {"type": "object", "properties": {"repository": {"$ref": "#/definitions/TaskRepositoryConfig"}}, "additionalProperties": false}, "TaskRepositoryConfig": {"type": "object", "properties": {"type": {"type": "string"}, "elasticsearch": {"$ref": "#/definitions/TaskElasticsearchv8ElasticTaskRepositoryConfig"}, "elasticClient": {"$ref": "#/definitions/CommonPersistenceElasticsearchElasticConfig"}}, "additionalProperties": false}, "TaskElasticsearchv8ElasticTaskRepositoryConfig": {"type": "object", "properties": {"indexName": {"type": "string"}, "mappingPath": {"type": "string"}, "refreshPolicy": {"type": "string"}, "enableOptimisticLocking": {"type": "boolean"}, "initializationTimeout": {"type": "number"}, "defaultSearchSize": {"type": "number"}, "defaultSearchSortField": {"type": "string"}, "defaultSearchSortOrder": {"type": "string"}, "fullTextSearchFields": {"type": "array", "items": {"type": "string"}}}, "required": ["indexName", "mappingPath", "refreshPolicy", "enableOptimisticLocking", "initializationTimeout", "defaultSearchSize", "defaultSearchSortField", "defaultSearchSortOrder", "fullTextSearchFields"], "additionalProperties": false}, "CommonPersistenceElasticsearchElasticConfig": {"type": "object", "properties": {"addresses": {"type": "array", "items": {"type": "string"}}, "logRequestBody": {"type": "boolean"}, "caPath": {"type": "string"}, "tlsInsecureSkipVerify": {"type": "boolean"}, "maxIdleConsPerHost": {"type": "number"}, "responseHeaderTimeoutSeconds": {"type": "number"}, "dialTimeoutSeconds": {"type": "number"}, "username": {"type": "string"}, "password": {"type": "string"}}, "additionalProperties": false}, "GraphConfig": {"type": "object", "properties": {"type": {"type": "string"}, "arangodb": {"$ref": "#/definitions/GraphRepositoryConfig"}, "arangoClient": {"$ref": "#/definitions/CommonPersistenceArangodbArangoConfig"}, "database": {"type": "string"}}, "additionalProperties": false}, "GraphRepositoryConfig": {"type": "object", "properties": {"levenshteinDistance": {"type": "number"}, "developerLevenshteinDistance": {"type": "number"}, "projectLevenshteinDistance": {"type": "number"}, "buildingLevenshteinDistance": {"type": "number"}, "brochureLevenshteinDistance": {"type": "number"}, "geoDistanceMeters": {"type": "number"}, "similarityQueryResultLimit": {"type": "number"}, "minLinkedEntitiesForCompilation": {"type": "number"}, "tenant": {"type": "string"}}, "required": ["levenshteinDistance", "developerLevenshteinDistance", "projectLevenshteinDistance", "buildingLevenshteinDistance", "brochureLevenshteinDistance", "geoDistanceMeters", "similarityQueryResultLimit", "minLinkedEntitiesForCompilation", "tenant"], "additionalProperties": false}, "CommonPersistenceArangodbArangoConfig": {"type": "object", "properties": {"endpoints": {"type": "array", "items": {"type": "string"}}, "caPath": {"type": "string"}, "tlsInsecureSkipVerify": {"type": "boolean"}, "maxIdleConsPerHost": {"type": "number"}, "responseHeaderTimeoutSeconds": {"type": "number"}, "dialTimeoutSeconds": {"type": "number"}, "username": {"type": "string"}, "password": {"type": "string"}, "maxConcurrentStreams": {"type": "number"}, "levenshteinDistance": {"type": "number"}, "geoDistanceMeters": {"type": "number"}}, "additionalProperties": false}, "IssueConfig": {"type": "object", "properties": {"maxIssuesPerType": {"type": "number"}}, "required": ["maxIssuesPerType"], "additionalProperties": false}, "SourceConfig": {"type": "object", "properties": {"SimilarityThreshold": {"type": "number"}, "GroupingThreshold": {"type": "number"}, "MaxResults": {"type": "number"}}, "required": ["Similarity<PERSON><PERSON><PERSON><PERSON>", "GroupingThreshold", "MaxResults"], "additionalProperties": false}, "EtlConfig": {"type": "object", "properties": {"postgres": {"$ref": "#/definitions/CommonPersistencePostgresConfig"}, "supportedEntityTypes": {"type": "array", "items": {"type": "string"}}, "defaultBatchSize": {"type": "number"}, "defaultTenant": {"type": "string"}, "entityConfigs": {"type": "object", "additionalProperties": {"$ref": "#/definitions/EtlEntityConfig"}}, "maxConcurrentWorkflows": {"type": "number"}, "supervisorIntervalSeconds": {"type": "number"}}, "additionalProperties": false}, "CommonPersistencePostgresConfig": {"type": "object", "properties": {"host": {"type": "string"}, "port": {"type": "number"}, "database": {"type": "string"}, "user": {"type": "string"}, "password": {"type": "string"}, "sslMode": {"type": "string"}, "caPath": {"type": "string"}, "minConns": {"type": "number"}, "maxConns": {"type": "number"}, "connTimeoutSeconds": {"type": "number"}}, "required": ["host", "port", "database", "user", "password", "sslMode", "caPath", "minConns", "max<PERSON>onns", "connTimeoutSeconds"], "additionalProperties": false}, "EtlEntityConfig": {"type": "object", "properties": {"batchSize": {"type": "number"}, "processingEnabled": {"type": "boolean"}, "supportedSources": {"type": "array", "items": {"type": "string"}}, "graphSyncEnabled": {"type": "boolean"}, "softDeleteEnabled": {"type": "boolean"}, "maxConcurrentSources": {"type": "number"}, "fastActivityTimeoutSeconds": {"type": "number"}, "slowActivityTimeoutSeconds": {"type": "number"}}, "additionalProperties": false}, "BackOfficeServerConfig": {"type": "object", "properties": {"image": {"type": "string", "description": "Docker image name without version/tag. Combined with imageVersion/imageTag to form the full image reference. Example: \"registry.example.com/realmond/backoffice\""}, "imageVersion": {"type": "string", "description": "Optional version for the Docker image. If both imageVersion and imageTag are specified, imageTag takes precedence. Example: \"1.0.0\""}, "imageTag": {"type": "string", "description": "Optional tag for the Docker image. Takes precedence over imageVersion if both are specified. Example: \"latest\" or \"stable\""}, "containerEnv": {"type": "object", "additionalProperties": {"oneOf": [{"type": "string"}, {"$ref": "#/definitions/SecureValueWrapper"}]}, "description": "Environment variables for the server container. Values can be plain strings or encrypted values using the secure wrapper."}, "secrets": {"$ref": "#/definitions/BackOfficeSecrets", "description": "Secret configuration for the component. Defines secrets to create or reference existing secrets."}, "port": {"type": "number", "description": "Port on which the server component listens. This is used to configure the Kubernetes Service and container port. The BackOffice API server typically uses port 3000 as specified in the Dockerfile."}, "replicas": {"type": "number", "description": "Number of replicas for the server deployment. If not specified, defaults to 1."}, "lbDomain": {"type": "string", "description": "Load balancer domain for ingress configuration. Used as the main domain for the ingress gateway."}, "dns": {"type": "array", "items": {"type": "object", "properties": {"targetDomain": {"type": "string"}}, "required": ["targetDomain"], "additionalProperties": false}, "description": "DNS configuration for additional domains. Each entry defines a target domain that will be added to the ingress."}, "cors": {"type": "object", "properties": {"enabled": {"type": "boolean", "description": "Whether CORS is enabled for this server. Default: false"}, "allowOrigins": {"type": "array", "items": {"type": "string"}, "description": "Allowed origins for CORS requests. Default: ['*'] (all origins)"}, "allowMethods": {"type": "array", "items": {"type": "string"}, "description": "Allowed methods for CORS requests. Default: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS']"}, "allowHeaders": {"type": "array", "items": {"type": "string"}, "description": "Allowed headers for CORS requests. Default: ['*'] (all headers)"}, "allowCredentials": {"type": "boolean", "description": "Whether to allow sending cookies in CORS requests. Default: true"}}, "additionalProperties": false, "description": "CORS configuration for the ingress. When enabled, allows cross-origin requests from frontend developers."}, "resources": {"type": "object", "properties": {"limits": {"type": "object", "properties": {"cpu": {"type": "string", "description": "CPU limit in Kubernetes format (e.g., \"500m\", \"1\")."}, "memory": {"type": "string", "description": "Memory limit in Kubernetes format (e.g., \"512Mi\", \"1Gi\")."}}, "additionalProperties": false, "description": "Resource limits beyond which the container will be terminated."}, "requests": {"type": "object", "properties": {"cpu": {"type": "string", "description": "CPU request in Kubernetes format (e.g., \"250m\", \"0.5\")."}, "memory": {"type": "string", "description": "Memory request in Kubernetes format (e.g., \"256Mi\", \"512Mi\")."}}, "additionalProperties": false, "description": "Resource requests that the container is guaranteed to receive."}}, "additionalProperties": false, "description": "Resource requirements for the server component. Maps directly to Kubernetes resource requests and limits."}, "appConfig": {"$ref": "#/definitions/AppConfig", "description": "Application configuration for the server component. This is serialized to YAML and mounted as a ConfigMap at /config/app.yaml. Configuration structure should match the Go application's config expectations."}}, "required": ["appConfig", "containerEnv", "image", "port", "secrets"], "additionalProperties": false, "description": "Server component specific configuration. Extends the base configuration with server-specific settings."}, "BackOfficeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Represents environment variables to be passed to the container. These are set as environment variables directly in the Kubernetes deployment."}, "BackOfficeSecrets": {"type": "object", "properties": {"kv": {"type": "object", "additionalProperties": {"$ref": "#/definitions/Input%3Cstring%3E"}, "description": "Key-value pairs of secrets to be created as a Kubernetes Secret. These are typically used for application-level secrets and are created by <PERSON><PERSON><PERSON> during deployment."}, "refs": {"type": "object", "additionalProperties": {"$ref": "#/definitions/Input%3Cstring%3E"}, "description": "References to existing secrets in the Kubernetes namespace. Instead of creating new secrets, this allows referencing pre-existing ones. Keys are environment variable names, values are secret references in the format \"secretName:secretKey\"."}, "arangoDB": {"type": "object", "properties": {"secretName": {"type": "string", "description": "Name of the existing Kubernetes Secret containing ArangoDB credentials. This secret must exist in the same namespace as the deployment."}, "hostKey": {"type": "string", "description": "Key in the secret containing the ArangoDB host. Defaults to \"host\" if not specified."}, "portKey": {"type": "string", "description": "Key in the secret containing the ArangoDB port. Defaults to \"port\" if not specified."}, "usernameKey": {"type": "string", "description": "Key in the secret containing the ArangoDB username. Defaults to \"username\" if not specified."}, "passwordKey": {"type": "string", "description": "Key in the secret containing the ArangoDB password. Defaults to \"password\" if not specified."}, "caKey": {"type": "string", "description": "Key in the secret containing the ArangoDB CA certificate for secure connections. Defaults to \"ca\" if not specified."}}, "required": ["secretName"], "additionalProperties": false, "description": "ArangoDB specific secret reference for database authentication and connection. References an existing Kubernetes Secret containing ArangoDB credentials. The secret is mounted as a volume at /etc/secrets/arango and environment variables are set to use these credentials."}, "elasticsearch": {"type": "object", "properties": {"addresses": {"type": "array", "items": {"type": "string"}, "description": "Array of Elasticsearch addresses to connect to (e.g. [\"https://es1:9200\", \"https://es2:9200\"])"}, "username": {"type": "string", "description": "Username for Elasticsearch authentication"}, "password": {"type": "string", "description": "Password for Elasticsearch authentication"}, "ca": {"type": "string", "description": "CA certificate content for secure connections. This will be mounted as a file in the container."}}, "required": ["addresses", "username", "password", "ca"], "additionalProperties": false, "description": "Elasticsearch configuration for search engine connectivity. These values will be used to create a secret and set up environment variables for the Elasticsearch client."}, "postgres": {"type": "object", "properties": {"host": {"type": "string", "description": "PostgreSQL host (e.g. \"localhost\", \"postgres.example.com\")"}, "port": {"type": "number", "description": "PostgreSQL port (typically 5432)"}, "database": {"type": "string", "description": "Database name to connect to"}, "user": {"type": "string", "description": "Username for PostgreSQL authentication"}, "password": {"type": "string", "description": "Password for PostgreSQL authentication"}, "sslMode": {"type": "string", "description": "SSL mode for connections (disable|require|verify-ca|verify-full)"}, "ca": {"type": "string", "description": "CA certificate content for secure connections. This will be mounted as a file in the container."}, "minConns": {"type": "number", "description": "Minimum number of connections in the pool"}, "maxConns": {"type": "number", "description": "Maximum number of connections in the pool"}, "connTimeoutSeconds": {"type": "number", "description": "Connection timeout in seconds"}}, "required": ["host", "port", "database", "user", "password", "sslMode", "minConns", "max<PERSON>onns", "connTimeoutSeconds"], "additionalProperties": false, "description": "PostgreSQL configuration for database connectivity. These values will be used to create a secret and set up environment variables for the PostgreSQL client in the ETL subsystem."}}, "required": ["kv"], "additionalProperties": false, "description": "Defines the secret configuration for BackOffice components."}, "Input<string>": {"anyOf": [{"type": "string"}, {"description": "Failed to correctly infer type"}, {"$ref": "#/definitions/OutputInstance%3Cstring%3E"}], "description": "[Input] is a property input for a resource.  It may be a promptly available T, a promise for one, or the output from a existing Resource."}, "OutputInstance<string>": {"type": "object", "additionalProperties": false, "description": "Instance side of the [Output<T>] type.  Exposes the deployment-time and run-time mechanisms for working with the underlying value of an [Output<T>]."}, "BackOfficeWorkerConfig": {"type": "object", "properties": {"image": {"type": "string", "description": "Docker image name without version/tag. Combined with imageVersion/imageTag to form the full image reference. Example: \"registry.example.com/realmond/backoffice\""}, "imageVersion": {"type": "string", "description": "Optional version for the Docker image. If both imageVersion and imageTag are specified, imageTag takes precedence. Example: \"1.0.0\""}, "imageTag": {"type": "string", "description": "Optional tag for the Docker image. Takes precedence over imageVersion if both are specified. Example: \"latest\" or \"stable\""}, "containerEnv": {"type": "object", "additionalProperties": {"oneOf": [{"type": "string"}, {"$ref": "#/definitions/SecureValueWrapper"}]}, "description": "Environment variables for the worker container. Values can be plain strings or encrypted values using the secure wrapper."}, "secrets": {"$ref": "#/definitions/BackOfficeSecrets", "description": "Secret configuration for the component. Defines secrets to create or reference existing secrets."}, "replicas": {"type": "number", "description": "Number of replicas for the worker deployment. If not specified, defaults to 1."}, "resources": {"type": "object", "properties": {"limits": {"type": "object", "properties": {"cpu": {"type": "string", "description": "CPU limit in Kubernetes format (e.g., \"500m\", \"1\")."}, "memory": {"type": "string", "description": "Memory limit in Kubernetes format (e.g., \"512Mi\", \"1Gi\")."}}, "additionalProperties": false, "description": "Resource limits beyond which the container will be terminated."}, "requests": {"type": "object", "properties": {"cpu": {"type": "string", "description": "CPU request in Kubernetes format (e.g., \"250m\", \"0.5\")."}, "memory": {"type": "string", "description": "Memory request in Kubernetes format (e.g., \"256Mi\", \"512Mi\")."}}, "additionalProperties": false, "description": "Resource requests that the container is guaranteed to receive."}}, "additionalProperties": false, "description": "Resource requirements for the worker component. Maps directly to Kubernetes resource requests and limits."}, "appConfig": {"$ref": "#/definitions/AppConfig", "description": "Application configuration for the worker component. This is serialized to YAML and mounted as a ConfigMap at /config/app.yaml. If not specified, the worker will use the root-level appConfig if available. Configuration structure should match the Go application's config expectations."}}, "additionalProperties": false, "required": ["containerEnv", "image", "secrets"], "description": "Worker component specific configuration. Extends the base configuration with worker-specific settings."}, "SecureValueWrapper": {"type": "object", "properties": {"secure": {"type": "string", "description": "Encrypted value in Pulumi format (v1:...)"}}}}, "properties": {"config": {"type": "object", "properties": {"backoffice-api:backoffice": {"$ref": "#/definitions/BackOfficeConfig"}, "backoffice-api:kubeconfig": {"oneOf": [{"type": "string"}, {"$ref": "#/definitions/SecureValueWrapper"}]}}}, "secretsprovider": {"type": "string"}, "encryptedkey": {"type": "string"}}}