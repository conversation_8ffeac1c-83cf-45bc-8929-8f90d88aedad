// Auto-generated types from app.schema.json

export interface AppConfig {
  cms?: CmsClientConfig;
  server?: ServerConfig;
  temporal?: TemporalConfig;
  task?: TaskConfig;
  graph?: GraphConfig;
  issue?: IssueConfig;
  source?: SourceConfig;
  etl?: EtlConfig;
}
export interface CmsClientConfig {
  baseUrl?: string;
  apiKey?: string;
  timeoutSeconds?: number;
}
export interface ServerConfig {
  address?: string;
  port?: number;
  middleware?: ServerMiddlewareConfig;
  auth?: ServerAuthConfig;
}
export interface ServerMiddlewareConfig {
  requestTimeoutMs?: number;
  openapi?: ServerMiddlewareOpenapiConfig;
  logger?: ServerMiddlewareRequestLoggerConfig;
}
export interface ServerMiddlewareOpenapiConfig {
  validateRequests?: boolean;
  validateResponses?: boolean;
}
export interface ServerMiddlewareRequestLoggerConfig {
  enabled?: boolean;
}
export interface ServerAuthConfig {
  jwksUrl?: string;
  audience?: string;
  issuer?: string;
  rolesClaimName?: string;
  rolesDomain?: string;
  rolePermissions?: {
    [k: string]: string[];
  };
}
export interface TemporalConfig {
  client?: TemporalClientConfig;
  worker?: TemporalWorkerOptions;
  task?: TemporalTaskConfig;
}
export interface TemporalClientConfig {
  hostPort?: string;
  namespace?: string;
  taskQueue?: string;
}
export interface TemporalWorkerOptions {
  maxConcurrentActivityExecutionSize?: number;
  workerActivitiesPerSecond?: number;
  maxConcurrentWorkflowTaskExecutionSize?: number;
  identity?: string;
}
export interface TemporalTaskConfig {
  fastActivityTimeoutSeconds?: number;
  slowActivityTimeoutSeconds?: number;
  taskCreationWaitTimeoutSeconds?: number;
  taskCreationPollIntervalMilliseconds?: number;
}
export interface TaskConfig {
  repository?: TaskRepositoryConfig;
}
export interface TaskRepositoryConfig {
  type?: string;
  elasticsearch?: TaskElasticsearchv8ElasticTaskRepositoryConfig;
  elasticClient?: CommonPersistenceElasticsearchElasticConfig;
}
export interface TaskElasticsearchv8ElasticTaskRepositoryConfig {
  indexName: string;
  mappingPath: string;
  refreshPolicy: string;
  enableOptimisticLocking: boolean;
  initializationTimeout: number;
  defaultSearchSize: number;
  defaultSearchSortField: string;
  defaultSearchSortOrder: string;
  fullTextSearchFields: string[];
}
export interface CommonPersistenceElasticsearchElasticConfig {
  addresses?: string[];
  logRequestBody?: boolean;
  caPath?: string;
  tlsInsecureSkipVerify?: boolean;
  maxIdleConsPerHost?: number;
  responseHeaderTimeoutSeconds?: number;
  dialTimeoutSeconds?: number;
  username?: string;
  password?: string;
}
export interface GraphConfig {
  type?: string;
  arangodb?: GraphRepositoryConfig;
  arangoClient?: CommonPersistenceArangodbArangoConfig;
  database?: string;
}
export interface GraphRepositoryConfig {
  levenshteinDistance: number;
  developerLevenshteinDistance: number;
  projectLevenshteinDistance: number;
  buildingLevenshteinDistance: number;
  brochureLevenshteinDistance: number;
  geoDistanceMeters: number;
  similarityQueryResultLimit: number;
  minLinkedEntitiesForCompilation: number;
  tenant: string;
}
export interface CommonPersistenceArangodbArangoConfig {
  endpoints?: string[];
  caPath?: string;
  tlsInsecureSkipVerify?: boolean;
  maxIdleConsPerHost?: number;
  responseHeaderTimeoutSeconds?: number;
  dialTimeoutSeconds?: number;
  username?: string;
  password?: string;
  maxConcurrentStreams?: number;
  levenshteinDistance?: number;
  geoDistanceMeters?: number;
}
export interface IssueConfig {
  maxIssuesPerType: number;
}
export interface SourceConfig {
  SimilarityThreshold: number;
  GroupingThreshold: number;
  MaxResults: number;
}
export interface EtlConfig {
  postgres?: CommonPersistencePostgresConfig;
  supportedEntityTypes?: string[];
  defaultBatchSize?: number;
  defaultTenant?: string;
  entityConfigs?: {
    [k: string]: EtlEntityConfig;
  };
  maxConcurrentWorkflows?: number;
  supervisorIntervalSeconds?: number;
}
export interface CommonPersistencePostgresConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  sslMode: string;
  caPath: string;
  minConns: number;
  maxConns: number;
  connTimeoutSeconds: number;
}
export interface EtlEntityConfig {
  batchSize?: number;
  processingEnabled?: boolean;
  supportedSources?: string[];
  graphSyncEnabled?: boolean;
  softDeleteEnabled?: boolean;
  maxConcurrentSources?: number;
  fastActivityTimeoutSeconds?: number;
  slowActivityTimeoutSeconds?: number;
}
