#!/usr/bin/env python3
"""
Detect serialization libraries used in minified JavaScript assets
"""

import re
import requests
import json
from typing import List, Dict, Any
from urllib.parse import urljoin, urlparse

def analyze_js_bundle(js_content: str) -> Dict[str, Any]:
    """
    Analyze JavaScript content to detect serialization libraries
    """
    
    # Library signatures to look for
    signatures = {
        'superjson': [
            r'superjson',
            r'SuperJSON',
            r'\.serialize\(',
            r'\.deserialize\(',
            r'allowedProps',
            r'transformValue'
        ],
        'devalue': [
            r'devalue',
            r'stringify.*unflatten',
            r'parse.*unflatten',
            r'HOLE',
            r'UNDEFINED',
            r'BIGINT'
        ],
        'flatted': [
            r'flatted',
            r'Flatted',
            r'stringify.*parse',
            r'\$\d+',  # Flatted uses $0, $1, etc.
            r'replacer.*reviver'
        ],
        'remix': [
            r'@remix-run',
            r'remix',
            r'loader.*data',
            r'action.*data',
            r'__remixContext',
            r'routeData'
        ],
        'nextjs': [
            r'__NEXT_DATA__',
            r'next/router',
            r'getServerSideProps',
            r'getStaticProps',
            r'_app\.js'
        ],
        'nuxtjs': [
            r'__NUXT__',
            r'nuxt',
            r'asyncData',
            r'serverPrefetch'
        ],
        'sveltekit': [
            r'__SVELTEKIT__',
            r'svelte',
            r'load.*function',
            r'page\.data'
        ],
        'custom_serializer': [
            r'_\d+.*:.*\d+',  # Pattern like "_1": 2
            r'circular.*ref',
            r'resolve.*value',
            r'lookup.*table'
        ]
    }
    
    results = {}
    
    for library, patterns in signatures.items():
        matches = []
        confidence = 0
        
        for pattern in patterns:
            found = re.findall(pattern, js_content, re.IGNORECASE)
            if found:
                matches.extend(found)
                confidence += len(found)
        
        if matches:
            results[library] = {
                'confidence': confidence,
                'matches': list(set(matches))[:10],  # First 10 unique matches
                'total_matches': len(matches)
            }
    
    return results

def extract_webpack_info(js_content: str) -> Dict[str, Any]:
    """
    Extract webpack module information
    """
    webpack_info = {}
    
    # Look for webpack module patterns
    webpack_patterns = {
        'webpack_version': r'webpack.*?(\d+\.\d+\.\d+)',
        'module_ids': r'__webpack_require__\((\d+)\)',
        'chunk_ids': r'webpackChunkName.*?"([^"]+)"',
        'module_exports': r'module\.exports\s*=',
        'require_calls': r'__webpack_require__'
    }
    
    for key, pattern in webpack_patterns.items():
        matches = re.findall(pattern, js_content, re.IGNORECASE)
        if matches:
            webpack_info[key] = matches[:5]  # First 5 matches
    
    return webpack_info

def analyze_network_requests(base_url: str) -> List[str]:
    """
    Find JavaScript assets from a webpage
    """
    try:
        response = requests.get(base_url, timeout=10)
        html_content = response.text
        
        # Find script tags
        script_patterns = [
            r'<script[^>]*src=["\']([^"\']+)["\']',
            r'<script[^>]*>.*?</script>',
        ]
        
        js_urls = []
        for pattern in script_patterns:
            matches = re.findall(pattern, html_content, re.DOTALL)
            for match in matches:
                if isinstance(match, str) and (match.endswith('.js') or 'chunk' in match):
                    full_url = urljoin(base_url, match)
                    js_urls.append(full_url)
        
        return js_urls[:10]  # Limit to first 10
    
    except Exception as e:
        print(f"Error analyzing {base_url}: {e}")
        return []

def detect_from_api_response(api_data: Any) -> Dict[str, Any]:
    """
    Analyze the API response structure to infer the serialization method
    """
    
    if not isinstance(api_data, list):
        return {'type': 'standard_json', 'confidence': 'high'}
    
    analysis = {
        'total_items': len(api_data),
        'has_numeric_refs': False,
        'has_underscore_keys': False,
        'has_negative_values': False,
        'has_circular_refs': False,
        'string_items': 0,
        'object_items': 0,
        'array_items': 0,
        'number_items': 0
    }
    
    # Analyze structure
    for item in api_data:
        if isinstance(item, str):
            analysis['string_items'] += 1
            if 'pages/' in item or 'routes/' in item:
                analysis['likely_framework'] = 'remix_or_nextjs'
        elif isinstance(item, dict):
            analysis['object_items'] += 1
            for key, value in item.items():
                if key.startswith('_') and key[1:].isdigit():
                    analysis['has_underscore_keys'] = True
                if isinstance(value, int) and value < 0:
                    analysis['has_negative_values'] = True
                if isinstance(value, int) and 0 <= value < len(api_data):
                    analysis['has_numeric_refs'] = True
        elif isinstance(item, list):
            analysis['array_items'] += 1
        elif isinstance(item, int):
            analysis['number_items'] += 1
    
    # Check for circular reference patterns
    if 'circular_ref' in str(api_data):
        analysis['has_circular_refs'] = True
    
    # Determine likely serialization method
    if analysis['has_underscore_keys'] and analysis['has_numeric_refs']:
        if analysis['has_negative_values']:
            return {
                'type': 'custom_reference_serialization',
                'confidence': 'high',
                'details': analysis,
                'likely_pattern': 'remix_or_custom_ssr'
            }
    
    return {
        'type': 'unknown',
        'confidence': 'low',
        'details': analysis
    }

def main():
    print("🔍 JavaScript Serialization Library Detector")
    print("=" * 60)
    
    # Method 1: Analyze the API response structure
    print("\n1️⃣  ANALYZING API RESPONSE STRUCTURE")
    print("-" * 40)
    
    try:
        with open('tmp.json', 'r') as f:
            api_data = json.load(f)
        
        api_analysis = detect_from_api_response(api_data)
        print(f"Detected type: {api_analysis['type']}")
        print(f"Confidence: {api_analysis['confidence']}")
        
        if 'likely_pattern' in api_analysis:
            print(f"Likely pattern: {api_analysis['likely_pattern']}")
        
        print(f"Structure details:")
        for key, value in api_analysis['details'].items():
            print(f"  {key}: {value}")
    
    except Exception as e:
        print(f"Error analyzing API response: {e}")
    
    # Method 2: Provide instructions for manual analysis
    print("\n2️⃣  MANUAL ANALYSIS TECHNIQUES")
    print("-" * 40)
    
    techniques = [
        "🌐 Browser DevTools:",
        "  - Open Network tab",
        "  - Look for .js files",
        "  - Search for 'superjson', 'devalue', 'flatted'",
        "",
        "🔍 Source Map Analysis:",
        "  - Look for .map files",
        "  - Check original source names",
        "",
        "📦 Package Detection:",
        "  - Check for __webpack_require__ calls",
        "  - Look for module IDs and chunk names",
        "",
        "🏷️  Framework Signatures:",
        "  - __NEXT_DATA__ = Next.js",
        "  - __NUXT__ = Nuxt.js", 
        "  - __remixContext = Remix",
        "  - __SVELTEKIT__ = SvelteKit"
    ]
    
    for technique in techniques:
        print(technique)
    
    # Method 3: Provide a JavaScript snippet for browser console
    print("\n3️⃣  BROWSER CONSOLE DETECTION SCRIPT")
    print("-" * 40)
    
    js_script = '''
// Paste this in browser console on the target site:

console.log("🔍 Detecting serialization libraries...");

// Check for common libraries
const libraries = {
    'SuperJSON': window.SuperJSON || window.superjson,
    'Devalue': window.devalue,
    'Flatted': window.Flatted || window.flatted,
    'Next.js': window.__NEXT_DATA__,
    'Nuxt.js': window.__NUXT__,
    'Remix': window.__remixContext,
    'SvelteKit': window.__SVELTEKIT__
};

Object.entries(libraries).forEach(([name, lib]) => {
    if (lib) console.log(`✅ Found: ${name}`, lib);
});

// Check webpack modules
if (window.__webpack_require__) {
    console.log("📦 Webpack detected");
    console.log("Modules:", Object.keys(window.__webpack_require__.cache || {}));
}

// Check for serialization patterns in scripts
const scripts = Array.from(document.scripts);
scripts.forEach((script, i) => {
    if (script.src) {
        console.log(`Script ${i}: ${script.src}`);
    }
});
'''
    
    print("Copy and paste this JavaScript code into your browser console:")
    print("```javascript")
    print(js_script)
    print("```")
    
    # Method 4: URL analysis
    print("\n4️⃣  URL ANALYSIS")
    print("-" * 40)
    print("If you have the website URL, you can:")
    print("1. Run: python detect_serialization_library.py <url>")
    print("2. This will download and analyze JS assets")
    
    # Save detection script
    with open('browser_detection.js', 'w') as f:
        f.write(js_script)
    
    print(f"\n💾 Browser detection script saved to: browser_detection.js")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        url = sys.argv[1]
        print(f"🌐 Analyzing JavaScript assets from: {url}")
        
        js_urls = analyze_network_requests(url)
        print(f"Found {len(js_urls)} JavaScript files:")
        
        for js_url in js_urls:
            print(f"  📄 {js_url}")
            try:
                response = requests.get(js_url, timeout=10)
                analysis = analyze_js_bundle(response.text)
                
                if analysis:
                    print(f"    Detected libraries:")
                    for lib, info in analysis.items():
                        print(f"      {lib}: confidence {info['confidence']}")
                else:
                    print(f"    No known libraries detected")
            except Exception as e:
                print(f"    Error analyzing: {e}")
    else:
        main()
