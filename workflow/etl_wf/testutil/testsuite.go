package testutil

import (
	"fmt"

	"gitlab.realmond.dev/realmond-app1/backoffice-api/common/converter"
	"gitlab.realmond.dev/realmond-app1/backoffice-api/etl"
	"gitlab.realmond.dev/realmond-app1/backoffice-api/etl/adapter"
	"gitlab.realmond.dev/realmond-app1/backoffice-api/etl/graphsync"
	"gitlab.realmond.dev/realmond-app1/backoffice-api/graph"
	"gitlab.realmond.dev/realmond-app1/backoffice-api/model"
	"gitlab.realmond.dev/realmond-app1/backoffice-api/workflow/etl_wf"
	"go.temporal.io/sdk/testsuite"
)

// ETLWorkflowTestSuite extends the Temporal WorkflowTestSuite with ETL-specific setup
type ETLWorkflowTestSuite struct {
	testsuite.WorkflowTestSuite
	SnapshotRepo *MockSnapshotRepository
	SCDRepo      *MockSCDRepository
	ProjectRepo  *MockProjectRepo
}

// SetupSuite is called once before all tests
func (ts *ETLWorkflowTestSuite) SetupSuite() {
	// Initialize mock repositories
	ts.SnapshotRepo = new(MockSnapshotRepository)
	ts.SCDRepo = new(MockSCDRepository)
	ts.ProjectRepo = new(MockProjectRepo)
}

// SetupTest is called before each test
func (ts *ETLWorkflowTestSuite) SetupTest() {
	// Reset mock repositories before each test
	ts.SnapshotRepo.ExpectedCalls = nil
	ts.SnapshotRepo.Calls = nil

	ts.SCDRepo.ExpectedCalls = nil
	ts.SCDRepo.Calls = nil

	ts.ProjectRepo.ExpectedCalls = nil
	ts.ProjectRepo.Calls = nil
}

// NewWorkflowTestEnv creates a new workflow test environment
func (ts *ETLWorkflowTestSuite) NewWorkflowTestEnv() *testsuite.TestWorkflowEnvironment {
	env := ts.NewTestWorkflowEnvironment()

	// Create a mock repository facade
	mockFacade := &MockRepositoryFacade{
		SnapshotRepo: ts.SnapshotRepo,
		SCDRepo:      ts.SCDRepo,
	}

	// Provide a minimal stub factory that returns the mocked project repository
	factory := &stubRepoFactory{projectRepo: ts.ProjectRepo}
	cfg := etl.NewConfig()
	convSvc := converter.NewEntityConverterService()
	gsSvc := graphsync.NewGraphSyncService(factory, cfg)
	activities := etl_wf.NewActivities(mockFacade, factory, cfg, convSvc, gsSvc)

	// Register ETL workflows
	env.RegisterWorkflow(etl_wf.SnapshotMultiEntitySupervisor)
	env.RegisterWorkflow(etl_wf.SnapshotToSCD)
	env.RegisterWorkflow(etl_wf.SCDToGraph)
	env.RegisterWorkflow(etl_wf.SnapshotSupervisor)

	// Register all ETL activities
	env.RegisterActivity(activities.ProcessSnapshotBatch)
	env.RegisterActivity(activities.ProcessSCDToGraph)
	env.RegisterActivity(activities.ListActiveSourceURNs)
	env.RegisterActivity(activities.GetGraphMaxUpdatedAt)

	return env
}

// NewActivityTestEnv creates a new activity test environment
func (ts *ETLWorkflowTestSuite) NewActivityTestEnv() *testsuite.TestActivityEnvironment {
	env := ts.NewTestActivityEnvironment()

	// Create a mock repository facade
	mockFacade := &MockRepositoryFacade{
		SnapshotRepo: ts.SnapshotRepo,
		SCDRepo:      ts.SCDRepo,
	}

	// Provide a minimal stub factory that returns the mocked project repository
	factory := &stubRepoFactory{projectRepo: ts.ProjectRepo}
	cfg2 := etl.NewConfig()
	convSvc2 := converter.NewEntityConverterService()
	gsSvc2 := graphsync.NewGraphSyncService(factory, cfg2)
	activities := etl_wf.NewActivities(mockFacade, factory, cfg2, convSvc2, gsSvc2)

	// Register all ETL activities
	env.RegisterActivity(activities.ProcessSnapshotBatch)
	env.RegisterActivity(activities.ProcessSCDToGraph)
	env.RegisterActivity(activities.ListActiveSourceURNs)
	env.RegisterActivity(activities.GetGraphMaxUpdatedAt)

	return env
}

// MockRepositoryFacade is a mock implementation of RepositoryFacadeInterface for testing
type MockRepositoryFacade struct {
	SnapshotRepo etl.SnapshotRepository
	SCDRepo      etl.SCDRepository
}

// Ensure MockRepositoryFacade implements RepositoryFacadeInterface
var _ etl.RepositoryFacadeInterface = (*MockRepositoryFacade)(nil)

// GetSnapshotRepository returns the mock snapshot repository
func (mf *MockRepositoryFacade) GetSnapshotRepository(entityType model.EntityType) etl.SnapshotRepository {
	return mf.SnapshotRepo
}

// GetSCDRepository returns the mock SCD repository
func (mf *MockRepositoryFacade) GetSCDRepository(entityType model.EntityType) etl.SCDRepository {
	return mf.SCDRepo
}

// GetRepositories returns both repositories for the entity type
func (mf *MockRepositoryFacade) GetRepositories(entityType model.EntityType) (etl.SnapshotRepository, etl.SCDRepository) {
	return mf.SnapshotRepo, mf.SCDRepo
}

// Project-specific convenience methods
func (mf *MockRepositoryFacade) ProjectRepositories() (etl.SnapshotRepository, etl.SCDRepository) {
	return mf.SnapshotRepo, mf.SCDRepo
}

func (mf *MockRepositoryFacade) DeveloperRepositories() (etl.SnapshotRepository, etl.SCDRepository) {
	return mf.SnapshotRepo, mf.SCDRepo
}

func (mf *MockRepositoryFacade) BuildingRepositories() (etl.SnapshotRepository, etl.SCDRepository) {
	return mf.SnapshotRepo, mf.SCDRepo
}

// RunWorkflowAndVerifySuccess is a helper to run a workflow and verify it completes without errors
func RunWorkflowAndVerifySuccess(env *testsuite.TestWorkflowEnvironment, workflowFn interface{}, workflowParams interface{}) {
	env.ExecuteWorkflow(workflowFn, workflowParams)

	// Verify workflow completed
	if !env.IsWorkflowCompleted() {
		panic("Workflow execution was not completed")
	}

	// Check if there was an error
	if err := env.GetWorkflowError(); err != nil {
		panic("Workflow execution failed: " + err.Error())
	}
}

type stubRepoFactory struct {
	projectRepo interface{}
}

func (f *stubRepoFactory) GetGraphRepository(entityType model.EntityType) (interface{}, error) {
	switch entityType {
	case model.EntityTypeProject:
		return adapter.NewTypedRepoAdapter[*graph.ArangoProject](f.projectRepo.(*MockProjectRepo)), nil
	default:
		return nil, fmt.Errorf("unsupported entity type %s", entityType)
	}
}
