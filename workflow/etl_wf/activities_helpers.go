package etl_wf

import (
	"strings"

	"gitlab.realmond.dev/realmond-app1/backoffice-api/etl"
	"gitlab.realmond.dev/realmond-app1/backoffice-api/model"
)

// shouldProcessSource determines whether a given sourceURN should be processed for the
// supplied entityType based on ETL configuration flags. Behaviour mirrors the
// reference logic in spec_v2 §3.2.
//
//   - Returns false when the entity type is not configured or disabled.
//   - When SupportedSources list is empty, all sources are allowed.
//   - Otherwise, the function returns true only if at least one configured
//     supported source substring is contained in the sourceURN.
func shouldProcessSource(cfg etl.Config, entityType model.EntityType, sourceURN string) bool {
	// Validate entity type against configuration – if unsupported, bail out early.
	if err := cfg.ValidateEntityType(entityType.String()); err != nil {
		return false
	}

	// Short-circuit using utility that already handles missing configs & disabled flag.
	if !cfg.IsEntityEnabled(entityType.String()) {
		return false
	}

	entityCfg, _ := cfg.GetEntityConfig(entityType.String()) // safe – we validated above

	// Empty slice means ALL sources are supported.
	if len(entityCfg.SupportedSources) == 0 {
		return true
	}

	// Check substring match against allowed sources.
	for _, supported := range entityCfg.SupportedSources {
		if strings.Contains(sourceURN, supported) {
			return true
		}
	}

	return false // Not in supported list
}
