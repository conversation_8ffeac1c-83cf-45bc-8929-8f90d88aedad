package etl_wf

import (
	"context"
	"fmt"
	"log"
	"time"

	"go.temporal.io/api/enums/v1"

	commonpb "go.temporal.io/api/common/v1"
	"go.temporal.io/api/serviceerror"
	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/converter"

	"gitlab.realmond.dev/realmond-app1/backoffice-api/common/buildinfo"
	"gitlab.realmond.dev/realmond-app1/backoffice-api/etl"
)

// EnsureSchedules makes sure the Temporal schedules that periodically
// trigger the ETL supervisor and SCD→graph workflows exist. If a schedule is
// already present it is left untouched, otherwise it will be created using the
// configured interval.
func EnsureSchedules(ctx context.Context, temporalClient client.Client, etlCfg etl.Config, taskQueue, tenant string) error {
	// Resolve running application version from environment (injected via deployment).
	// Empty string is treated as "dev" and will not trigger memo mismatch unless supplied.
	appVersion := buildinfo.Version()

	// Helper closure to upsert a schedule.
	upsert := func(scheduleID string, every time.Duration, wfID string, wf interface{}, args []interface{}) error {
		sc := temporalClient.ScheduleClient()
		handle := sc.GetHandle(ctx, scheduleID)

		// Check if schedule exists and whether it matches the desired spec.
		desc, err := handle.Describe(ctx)
		if err == nil {
			intervalMatches := desc != nil && desc.Schedule.Spec != nil && len(desc.Schedule.Spec.Intervals) == 1 && desc.Schedule.Spec.Intervals[0].Every == every

			// Compare memo appVersion when it exists in the schedule description.  If the
			// memo key is absent we assume the schedule predates version tagging and
			// therefore do NOT treat it as a mismatch. This behaviour aligns with
			// ensure_schedules_test expectations (no-op when interval unchanged).
			memoMatches := true
			if desc != nil && desc.Memo != nil {
				if p, ok := desc.Memo.Fields["appVersion"]; ok {
					var v string
					dc := converter.GetDefaultDataConverter()
					if err := dc.FromPayload(p, &v); err == nil && v == appVersion {
						memoMatches = true
					} else {
						memoMatches = false
					}
				}
			}

			if intervalMatches && memoMatches {
				log.Printf("Temporal schedule %s already up-to-date", scheduleID)
				return nil
			}

			// Update existing schedule with new spec and memo
			log.Printf("Updating schedule %s (interval/memo change detected)", scheduleID)
			return handle.Update(ctx, client.ScheduleUpdateOptions{
				DoUpdate: func(input client.ScheduleUpdateInput) (*client.ScheduleUpdate, error) {
					// Update interval
					input.Description.Schedule.Spec = &client.ScheduleSpec{
						Intervals: []client.ScheduleIntervalSpec{{Every: every}},
					}

					// Refresh memo with current version
					dc := converter.GetDefaultDataConverter()
					payload, _ := dc.ToPayload(appVersion)
					input.Description.Memo = &commonpb.Memo{Fields: map[string]*commonpb.Payload{"appVersion": payload}}
					return &client.ScheduleUpdate{Schedule: &input.Description.Schedule}, nil
				},
			})
		}

		// Only create if not-found; propagate other errors.
		if _, ok := err.(*serviceerror.NotFound); !ok {
			return fmt.Errorf("describe schedule %s: %w", scheduleID, err)
		}

		// Create schedule with the provided spec.
		_, err = sc.Create(ctx, client.ScheduleOptions{
			ID: scheduleID,
			Spec: client.ScheduleSpec{
				Intervals: []client.ScheduleIntervalSpec{{Every: every}},
			},
			Action: &client.ScheduleWorkflowAction{
				ID:        wfID,
				TaskQueue: taskQueue,
				Workflow:  wf,
				Args:      args,
			},
			Memo:    map[string]interface{}{"appVersion": appVersion},
			Overlap: enums.SCHEDULE_OVERLAP_POLICY_SKIP,
		})
		if err != nil {
			return fmt.Errorf("create schedule %s: %w", scheduleID, err)
		}
		log.Printf("Temporal schedule %s created (every %s)", scheduleID, every.String())
		return nil
	}

	// ---------------------------------------------------------------------
	// Multi-Entity Supervisor Schedule
	// ---------------------------------------------------------------------

	supervisorInterval := etlCfg.SupervisorIntervalSeconds
	if supervisorInterval <= 0 {
		supervisorInterval = 300 // fallback to 5 min
	}

	// Derive enabled entity types from configuration (ProcessingEnabled == true)
	entityTypes := make([]string, 0, len(etlCfg.EntityConfigs))
	for et, cfg := range etlCfg.EntityConfigs {
		if cfg.ProcessingEnabled {
			entityTypes = append(entityTypes, et)
		}
	}

	if err := upsert(
		"multi-entity-supervisor-schedule",
		time.Duration(supervisorInterval)*time.Second,
		"multi-entity-supervisor",
		SnapshotMultiEntitySupervisor,
		[]interface{}{SupervisorWorkflowParams{
			EntityTypes:                entityTypes,
			BatchSize:                  etlCfg.DefaultBatchSize,
			FastActivityTimeoutSeconds: 30, // sensible default; fine-tuned per entity later
			MaxConcurrentWorkflows:     etlCfg.MaxConcurrentWorkflows,
		}},
	); err != nil {
		return err
	}

	// ---------------------------------------------------------------------
	// Per-Entity SCD→Graph Schedules
	// ---------------------------------------------------------------------

	for et, cfg := range etlCfg.EntityConfigs {
		if !cfg.GraphSyncEnabled {
			continue // skip disabled entities
		}

		intervalSeconds := cfg.SlowActivityTimeoutSeconds
		if intervalSeconds <= 0 {
			intervalSeconds = 300 // sensible default (5 min)
		}

		scheduleID := fmt.Sprintf("%s-scd-to-graph-schedule", et)
		wfID := fmt.Sprintf("scd-to-graph::%s", et)

		if err := upsert(
			scheduleID,
			time.Duration(intervalSeconds)*time.Second,
			wfID,
			SCDToGraph,
			[]interface{}{SCDToGraphWorkflowParams{
				EntityType:                 et,
				Tenant:                     tenant,
				BatchSize:                  cfg.BatchSize,
				FastActivityTimeoutSeconds: cfg.FastActivityTimeoutSeconds,
				SlowActivityTimeoutSeconds: cfg.SlowActivityTimeoutSeconds,
			}},
		); err != nil {
			return err
		}
	}

	return nil
}
