{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://gitlab.realmond.dev/realmond-app1/backoffice-api/config/config-config", "$defs": {"CmsClientConfig": {"properties": {"baseUrl": {"type": "string"}, "apiKey": {"type": "string"}, "timeoutSeconds": {"type": "integer"}}, "additionalProperties": false, "type": "object"}, "CommonPersistenceArangodbArangoConfig": {"properties": {"endpoints": {"items": {"type": "string"}, "type": "array"}, "caPath": {"type": "string"}, "tlsInsecureSkipVerify": {"type": "boolean"}, "maxIdleConsPerHost": {"type": "integer"}, "responseHeaderTimeoutSeconds": {"type": "integer"}, "dialTimeoutSeconds": {"type": "integer"}, "username": {"type": "string"}, "password": {"type": "string"}, "maxConcurrentStreams": {"type": "integer"}, "levenshteinDistance": {"type": "integer"}, "geoDistanceMeters": {"type": "integer"}}, "additionalProperties": false, "type": "object"}, "CommonPersistenceElasticsearchElasticConfig": {"properties": {"addresses": {"items": {"type": "string"}, "type": "array"}, "logRequestBody": {"type": "boolean"}, "caPath": {"type": "string"}, "tlsInsecureSkipVerify": {"type": "boolean"}, "maxIdleConsPerHost": {"type": "integer"}, "responseHeaderTimeoutSeconds": {"type": "integer"}, "dialTimeoutSeconds": {"type": "integer"}, "username": {"type": "string"}, "password": {"type": "string"}}, "additionalProperties": false, "type": "object"}, "CommonPersistencePostgresConfig": {"properties": {"host": {"type": "string"}, "port": {"type": "integer"}, "database": {"type": "string"}, "user": {"type": "string"}, "password": {"type": "string"}, "sslMode": {"type": "string"}, "caPath": {"type": "string"}, "minConns": {"type": "integer"}, "maxConns": {"type": "integer"}, "connTimeoutSeconds": {"type": "integer"}}, "additionalProperties": false, "type": "object", "required": ["host", "port", "database", "user", "password", "sslMode", "caPath", "minConns", "max<PERSON>onns", "connTimeoutSeconds"]}, "EtlConfig": {"properties": {"postgres": {"$ref": "#/$defs/CommonPersistencePostgresConfig"}, "supportedEntityTypes": {"items": {"type": "string"}, "type": "array"}, "defaultBatchSize": {"type": "integer"}, "defaultTenant": {"type": "string"}, "entityConfigs": {"additionalProperties": {"$ref": "#/$defs/EtlEntityConfig"}, "type": "object"}, "maxConcurrentWorkflows": {"type": "integer"}, "supervisorIntervalSeconds": {"type": "integer"}}, "additionalProperties": false, "type": "object"}, "EtlEntityConfig": {"properties": {"batchSize": {"type": "integer"}, "processingEnabled": {"type": "boolean"}, "supportedSources": {"items": {"type": "string"}, "type": "array"}, "graphSyncEnabled": {"type": "boolean"}, "softDeleteEnabled": {"type": "boolean"}, "maxConcurrentSources": {"type": "integer"}, "fastActivityTimeoutSeconds": {"type": "integer"}, "slowActivityTimeoutSeconds": {"type": "integer"}}, "additionalProperties": false, "type": "object"}, "GraphConfig": {"properties": {"type": {"type": "string"}, "arangodb": {"$ref": "#/$defs/GraphRepositoryConfig"}, "arangoClient": {"$ref": "#/$defs/CommonPersistenceArangodbArangoConfig"}, "database": {"type": "string"}}, "additionalProperties": false, "type": "object"}, "GraphRepositoryConfig": {"properties": {"levenshteinDistance": {"type": "integer"}, "developerLevenshteinDistance": {"type": "integer"}, "projectLevenshteinDistance": {"type": "integer"}, "buildingLevenshteinDistance": {"type": "integer"}, "brochureLevenshteinDistance": {"type": "integer"}, "geoDistanceMeters": {"type": "integer"}, "similarityQueryResultLimit": {"type": "integer"}, "minLinkedEntitiesForCompilation": {"type": "integer"}, "tenant": {"type": "string"}}, "additionalProperties": false, "type": "object", "required": ["levenshteinDistance", "developerLevenshteinDistance", "projectLevenshteinDistance", "buildingLevenshteinDistance", "brochureLevenshteinDistance", "geoDistanceMeters", "similarityQueryResultLimit", "minLinkedEntitiesForCompilation", "tenant"]}, "IssueConfig": {"properties": {"maxIssuesPerType": {"type": "integer"}}, "additionalProperties": false, "type": "object", "required": ["maxIssuesPerType"]}, "ServerAuthConfig": {"properties": {"jwksUrl": {"type": "string"}, "audience": {"type": "string"}, "issuer": {"type": "string"}, "rolesClaimName": {"type": "string"}, "rolesDomain": {"type": "string"}, "rolePermissions": {"additionalProperties": {"items": {"type": "string"}, "type": "array"}, "type": "object"}}, "additionalProperties": false, "type": "object"}, "ServerConfig": {"properties": {"address": {"type": "string"}, "port": {"type": "integer"}, "middleware": {"$ref": "#/$defs/ServerMiddlewareConfig"}, "auth": {"$ref": "#/$defs/ServerAuthConfig"}}, "additionalProperties": false, "type": "object"}, "ServerMiddlewareConfig": {"properties": {"requestTimeoutMs": {"type": "integer"}, "openapi": {"$ref": "#/$defs/ServerMiddlewareOpenapiConfig"}, "logger": {"$ref": "#/$defs/ServerMiddlewareRequestLoggerConfig"}}, "additionalProperties": false, "type": "object"}, "ServerMiddlewareOpenapiConfig": {"properties": {"validateRequests": {"type": "boolean"}, "validateResponses": {"type": "boolean"}}, "additionalProperties": false, "type": "object"}, "ServerMiddlewareRequestLoggerConfig": {"properties": {"enabled": {"type": "boolean"}}, "additionalProperties": false, "type": "object"}, "SourceConfig": {"properties": {"SimilarityThreshold": {"type": "number"}, "GroupingThreshold": {"type": "number"}, "MaxResults": {"type": "integer"}}, "additionalProperties": false, "type": "object", "required": ["Similarity<PERSON><PERSON><PERSON><PERSON>", "GroupingThreshold", "MaxResults"]}, "TaskConfig": {"properties": {"repository": {"$ref": "#/$defs/TaskRepositoryConfig"}}, "additionalProperties": false, "type": "object"}, "TaskElasticsearchv8ElasticTaskRepositoryConfig": {"properties": {"indexName": {"type": "string"}, "mappingPath": {"type": "string"}, "refreshPolicy": {"type": "string"}, "enableOptimisticLocking": {"type": "boolean"}, "initializationTimeout": {"type": "integer"}, "defaultSearchSize": {"type": "integer"}, "defaultSearchSortField": {"type": "string"}, "defaultSearchSortOrder": {"type": "string"}, "fullTextSearchFields": {"items": {"type": "string"}, "type": "array"}}, "additionalProperties": false, "type": "object", "required": ["indexName", "mappingPath", "refreshPolicy", "enableOptimisticLocking", "initializationTimeout", "defaultSearchSize", "defaultSearchSortField", "defaultSearchSortOrder", "fullTextSearchFields"]}, "TaskRepositoryConfig": {"properties": {"type": {"type": "string"}, "elasticsearch": {"$ref": "#/$defs/TaskElasticsearchv8ElasticTaskRepositoryConfig"}, "elasticClient": {"$ref": "#/$defs/CommonPersistenceElasticsearchElasticConfig"}}, "additionalProperties": false, "type": "object"}, "TemporalClientConfig": {"properties": {"hostPort": {"type": "string"}, "namespace": {"type": "string"}, "taskQueue": {"type": "string"}}, "additionalProperties": false, "type": "object"}, "TemporalConfig": {"properties": {"client": {"$ref": "#/$defs/TemporalClientConfig"}, "worker": {"$ref": "#/$defs/TemporalWorkerOptions"}, "task": {"$ref": "#/$defs/TemporalTaskConfig"}}, "additionalProperties": false, "type": "object"}, "TemporalTaskConfig": {"properties": {"fastActivityTimeoutSeconds": {"type": "integer"}, "slowActivityTimeoutSeconds": {"type": "integer"}, "taskCreationWaitTimeoutSeconds": {"type": "integer"}, "taskCreationPollIntervalMilliseconds": {"type": "integer"}}, "additionalProperties": false, "type": "object"}, "TemporalWorkerOptions": {"properties": {"maxConcurrentActivityExecutionSize": {"type": "integer"}, "workerActivitiesPerSecond": {"type": "number"}, "maxConcurrentWorkflowTaskExecutionSize": {"type": "integer"}, "identity": {"type": "string"}}, "additionalProperties": false, "type": "object"}}, "properties": {"cms": {"$ref": "#/$defs/CmsClientConfig"}, "server": {"$ref": "#/$defs/ServerConfig"}, "temporal": {"$ref": "#/$defs/TemporalConfig"}, "task": {"$ref": "#/$defs/TaskConfig"}, "graph": {"$ref": "#/$defs/GraphConfig"}, "issue": {"$ref": "#/$defs/IssueConfig"}, "source": {"$ref": "#/$defs/SourceConfig"}, "etl": {"$ref": "#/$defs/EtlConfig"}}, "additionalProperties": false, "type": "object"}