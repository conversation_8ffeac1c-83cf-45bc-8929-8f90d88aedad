package cmd

import (
	"fmt"
	"os"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"

	"github.com/spf13/cobra"
	"gitlab.realmond.dev/realmond-app1/backoffice-api/common/buildinfo"
)

var (
	logLevel string
	version  bool
)

func init() {
	RootCmd.PersistentFlags().StringVar(&logLevel, "log-level", "info", "Set the logging level (debug, info, warn, error, fatal, panic)")
	RootCmd.Flags().BoolVar(&version, "version", false, "Print version information and exit")
}

var RootCmd = &cobra.Command{
	Use:   "app",
	Short: "backoffice",
	PreRun: func(cmd *cobra.Command, args []string) {
		if version {
			fmt.Println(buildinfo.Version())
			os.Exit(0)
		}
	},
	Run: func(cmd *cobra.Command, args []string) {
		// If we reach here, no version flag was set and no subcommand was provided
		cmd.Help()
	},
	PersistentPreRun: func(cmd *cobra.Command, args []string) {
		setupLogging()
	},
}

func setupLogging() {
	level, err := zerolog.ParseLevel(logLevel)
	if err != nil {
		level = zerolog.InfoLevel
		log.Warn().Str("logLevel", logLevel).Msg("unknown log level. falling back to info level")
	}
	zerolog.SetGlobalLevel(level)
	logger := log.With().Caller().Logger()
	if profile == "local" && appConfigPath == "" {
		// use a  human-friendly, colorized output in local setup
		logger = logger.Output(zerolog.ConsoleWriter{Out: os.Stderr})
	}
	log.Logger = logger
}

// Execute executes the root command
func Execute() {
	err := RootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}
