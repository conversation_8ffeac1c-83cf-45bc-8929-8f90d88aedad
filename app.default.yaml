cms:
    baseUrl: http://localhost:1337
    timeoutSeconds: 30
etl:
    defaultBatchSize: 1000
    defaultTenant: default
    entityConfigs:
        buildings:
            batchSize: 1500
            fastActivityTimeoutSeconds: 30
            graphSyncEnabled: true
            maxConcurrentSources: 8
            processingEnabled: true
            slowActivityTimeoutSeconds: 600
            softDeleteEnabled: true
        developers:
            batchSize: 500
            fastActivityTimeoutSeconds: 30
            graphSyncEnabled: true
            maxConcurrentSources: 5
            processingEnabled: true
            slowActivityTimeoutSeconds: 600
            softDeleteEnabled: true
        projects:
            batchSize: 1000
            fastActivityTimeoutSeconds: 30
            graphSyncEnabled: true
            maxConcurrentSources: 10
            processingEnabled: true
            slowActivityTimeoutSeconds: 600
            softDeleteEnabled: true
    maxConcurrentWorkflows: 50
    postgres:
        caPath: null
        connTimeoutSeconds: 5
        database: postgres
        host: localhost
        maxConns: 20
        minConns: 2
        password: postgres
        port: 5432
        sslMode: disable
        user: postgres
    supervisorIntervalSeconds: 300
    supportedEntityTypes:
        - projects
        - developers
        - buildings
graph:
    arangoClient:
        dialTimeoutSeconds: 30
        endpoints:
            - http://localhost:8529
        maxConcurrentStreams: 100
        maxIdleConsPerHost: 10
        responseHeaderTimeoutSeconds: 30
    arangodb:
        brochureLevenshteinDistance: 3
        buildingLevenshteinDistance: 3
        developerLevenshteinDistance: 3
        geoDistanceMeters: 2000
        levenshteinDistance: 3
        minLinkedEntitiesForCompilation: 2
        projectLevenshteinDistance: 3
        similarityQueryResultLimit: 100
        tenant: backoffice-api-local
    database: realmond-backoffice
    type: arangodb
issue:
    maxIssuesPerType: 100
server:
    address: 0.0.0.0
    auth:
        audience: YOUR_SPA_CLIENT_ID
        issuer: https://realmond-intra.eu.auth0.com/
        jwksUrl: https://realmond-intra.eu.auth0.com/.well-known/jwks.json
        rolePermissions:
            admin.backoffice:
                - issues:read
                - tasks:create
                - tasks:read
                - tasks:claim
                - tasks:submit
                - tasks:review
                - projects:all
                - developers:all
                - floor-plans:all
                - payment-plans:all
                - reviews:create
                - reviews:read
                - reviews:update
                - reviews:delete
                - users:read
                - sources:read
            content-manager.backoffice:
                - tasks:read
                - tasks:claim
                - tasks:submit
                - sources:read
            content-moderator.backoffice:
                - issues:read
                - tasks:read
                - tasks:claim
                - tasks:submit
                - tasks:review
                - users:read
                - issues:read
                - tasks:create
                - sources:read
            content-reviewer.backoffice:
                - tasks:read
                - tasks:review
                - sources:read
        rolesClaimName: https://back-office-api/roles
        rolesDomain: realmond.dev
    middleware:
        logger:
            enabled: true
        openapi:
            validateRequests: true
            validateResponses: true
        requestTimeoutMs: 30000
    port: 3000
source:
    GroupingThreshold: 0.9
    MaxResults: 50
    SimilarityThreshold: 0.7
task:
    repository:
        elasticClient:
            addresses:
                - http://localhost:9200
            dialTimeoutSeconds: 30
            maxIdleConsPerHost: 10
            responseHeaderTimeoutSeconds: 30
        elasticsearch:
            defaultSearchSize: 10
            defaultSearchSortField: _metadata.updatedAt
            defaultSearchSortOrder: desc
            enableOptimisticLocking: true
            fullTextSearchFields:
                - title
                - review
                - data.input
            indexName: tasks
            initializationTimeout: 3e+10
            mappingPath: ""
            refreshPolicy: "false"
        type: elasticsearch
temporal:
    client:
        hostPort: localhost:7233
        namespace: default
        taskQueue: backoffice-api
    task:
        fastActivityTimeoutSeconds: 10
        slowActivityTimeoutSeconds: 300
        taskCreationPollIntervalMilliseconds: 200
        taskCreationWaitTimeoutSeconds: 5
    worker:
        identity: backoffice-api-worker
        maxConcurrentActivityExecutionSize: 100
        maxConcurrentWorkflowTaskExecutionSize: 100
        workerActivitiesPerSecond: 100
