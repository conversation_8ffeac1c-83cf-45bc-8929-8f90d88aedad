#!/usr/bin/env node
/**
 * Comprehensive decoder using various serialization libraries
 * Tests Remix, devalue, flatted, superjson, and custom approaches
 */

const fs = require('fs');
const path = require('path');

// Try to import libraries (some might not be available)
let devalue, flatted, superjson;

try {
    devalue = require('devalue');
    console.log('✅ devalue loaded');
} catch (e) {
    console.log('❌ devalue not available');
}

try {
    flatted = require('flatted');
    console.log('✅ flatted loaded');
} catch (e) {
    console.log('❌ flatted not available');
}

try {
    superjson = require('superjson');
    console.log('✅ superjson loaded');
} catch (e) {
    console.log('❌ superjson not available');
}

// Custom decoder based on our analysis
function customDecode(data) {
    if (!Array.isArray(data)) {
        return { error: 'Data is not an array' };
    }
    
    function resolveValue(value, visited = new Set(), depth = 0) {
        if (depth > 50) return '<max_depth>';
        
        if (typeof value === 'number') {
            if (value === -5) return null;
            if (value < 0) return null;
            if (visited.has(value)) return `<circular_${value}>`;
            if (value >= 0 && value < data.length) {
                visited.add(value);
                const result = resolveValue(data[value], new Set(visited), depth + 1);
                visited.delete(value);
                return result;
            }
            return value;
        }
        
        if (typeof value === 'object' && value !== null) {
            if (Array.isArray(value)) {
                return value.map(item => resolveValue(item, new Set(visited), depth + 1));
            } else {
                const resolved = {};
                for (const [key, val] of Object.entries(value)) {
                    resolved[key] = resolveValue(val, new Set(visited), depth + 1);
                }
                return resolved;
            }
        }
        
        return value;
    }
    
    // Find the compound data
    let compoundData = null;
    let route = null;
    
    // Look for route
    for (const item of data) {
        if (typeof item === 'string' && item.includes('pages/')) {
            route = item;
            break;
        }
    }
    
    // Look for compound data
    for (let i = 0; i < data.length; i++) {
        if (data[i] === 'compound' && i + 1 < data.length) {
            const compoundRef = data[i + 1];
            if (typeof compoundRef === 'object' && compoundRef !== null) {
                compoundData = resolveValue(compoundRef);
                break;
            }
        }
    }
    
    return {
        route,
        compound: compoundData,
        success: compoundData !== null
    };
}

// Test different decoders
function testDecoders(jsonData) {
    const results = {};
    
    console.log('\n🔍 Testing different decoders...\n');
    
    // 1. Custom decoder
    console.log('1️⃣  Testing custom decoder...');
    try {
        const customResult = customDecode(jsonData);
        results.custom = {
            success: customResult.success,
            route: customResult.route,
            hasCompound: !!customResult.compound,
            compoundKeys: customResult.compound ? Object.keys(customResult.compound).length : 0,
            data: customResult
        };
        console.log(`   ✅ Success: ${customResult.success}`);
        console.log(`   📍 Route: ${customResult.route || 'not found'}`);
        console.log(`   🗂️  Compound keys: ${results.custom.compoundKeys}`);
    } catch (error) {
        results.custom = { success: false, error: error.message };
        console.log(`   ❌ Error: ${error.message}`);
    }
    
    // 2. Devalue
    if (devalue) {
        console.log('\n2️⃣  Testing devalue...');
        try {
            // Devalue expects a string, so we need to stringify first
            const stringified = JSON.stringify(jsonData);
            const devalueResult = devalue.parse(stringified);
            results.devalue = {
                success: true,
                type: typeof devalueResult,
                isArray: Array.isArray(devalueResult),
                length: devalueResult?.length || 0
            };
            console.log(`   ✅ Parsed successfully`);
            console.log(`   📊 Type: ${results.devalue.type}`);
        } catch (error) {
            results.devalue = { success: false, error: error.message };
            console.log(`   ❌ Error: ${error.message}`);
        }
    }
    
    // 3. Flatted
    if (flatted) {
        console.log('\n3️⃣  Testing flatted...');
        try {
            const stringified = JSON.stringify(jsonData);
            const flattedResult = flatted.parse(stringified);
            results.flatted = {
                success: true,
                type: typeof flattedResult,
                isArray: Array.isArray(flattedResult),
                length: flattedResult?.length || 0
            };
            console.log(`   ✅ Parsed successfully`);
            console.log(`   📊 Type: ${results.flatted.type}`);
        } catch (error) {
            results.flatted = { success: false, error: error.message };
            console.log(`   ❌ Error: ${error.message}`);
        }
    }
    
    // 4. SuperJSON
    if (superjson) {
        console.log('\n4️⃣  Testing superjson...');
        try {
            const superjsonResult = superjson.deserialize({ json: jsonData });
            results.superjson = {
                success: true,
                type: typeof superjsonResult,
                isArray: Array.isArray(superjsonResult),
                length: superjsonResult?.length || 0
            };
            console.log(`   ✅ Parsed successfully`);
            console.log(`   📊 Type: ${results.superjson.type}`);
        } catch (error) {
            results.superjson = { success: false, error: error.message };
            console.log(`   ❌ Error: ${error.message}`);
        }
    }
    
    // 5. Try as Remix turbo-stream format
    console.log('\n5️⃣  Testing Remix turbo-stream format...');
    try {
        // This is a guess at how Remix might structure the data
        // Look for specific patterns that indicate turbo-stream
        let remixData = null;
        
        if (Array.isArray(jsonData)) {
            // Look for loader data pattern
            for (let i = 0; i < jsonData.length - 1; i++) {
                if (jsonData[i] === 'loader' || jsonData[i] === 'data') {
                    remixData = jsonData[i + 1];
                    break;
                }
            }
            
            // If not found, try the compound approach
            if (!remixData) {
                for (let i = 0; i < jsonData.length - 1; i++) {
                    if (jsonData[i] === 'compound') {
                        remixData = jsonData[i + 1];
                        break;
                    }
                }
            }
        }
        
        results.remix = {
            success: !!remixData,
            type: typeof remixData,
            hasData: !!remixData,
            data: remixData
        };
        
        console.log(`   ${remixData ? '✅' : '❌'} Found Remix-style data: ${!!remixData}`);
        
    } catch (error) {
        results.remix = { success: false, error: error.message };
        console.log(`   ❌ Error: ${error.message}`);
    }
    
    return results;
}

// Extract meaningful information from the best result
function extractProjectInfo(decodedData) {
    if (!decodedData || !decodedData.compound) {
        return null;
    }
    
    const compound = decodedData.compound;
    const info = {};
    
    // Map known fields
    const fieldMap = {
        '_7': 'id',
        '_10': 'title',
        '_12': 'description',
        '_73': 'district',
        '_75': 'address',
        '_77': 'builder',
        '_22': 'website',
        '_53': 'dld_project_number',
        '_55': 'construction_percent',
        '_16': 'start_date',
        '_18': 'planned_completion',
        '_20': 'predicted_completion',
        '_21': 'actual_completion'
    };
    
    for (const [key, value] of Object.entries(compound)) {
        const mappedKey = fieldMap[key] || key;
        info[mappedKey] = value;
    }
    
    return info;
}

// Main function
function main() {
    const filename = process.argv[2] || 'tmp.json';
    
    if (!fs.existsSync(filename)) {
        console.error(`❌ File ${filename} not found`);
        process.exit(1);
    }
    
    console.log(`🔍 Analyzing ${filename} with multiple decoders...`);
    
    try {
        const rawData = JSON.parse(fs.readFileSync(filename, 'utf8'));
        console.log(`📊 Raw data: ${Array.isArray(rawData) ? 'Array' : typeof rawData} with ${rawData.length || 'unknown'} items`);
        
        const results = testDecoders(rawData);
        
        // Find the best result
        let bestResult = null;
        let bestScore = 0;
        
        for (const [method, result] of Object.entries(results)) {
            if (result.success) {
                let score = 1;
                if (result.hasCompound) score += 3;
                if (result.compoundKeys > 10) score += 2;
                if (result.route) score += 1;
                
                if (score > bestScore) {
                    bestScore = score;
                    bestResult = { method, ...result };
                }
            }
        }
        
        console.log('\n' + '='.repeat(60));
        console.log('📋 RESULTS SUMMARY');
        console.log('='.repeat(60));
        
        for (const [method, result] of Object.entries(results)) {
            const status = result.success ? '✅' : '❌';
            console.log(`${status} ${method.padEnd(12)} - ${result.success ? 'Success' : result.error}`);
        }
        
        if (bestResult) {
            console.log('\n' + '='.repeat(60));
            console.log(`🏆 BEST RESULT: ${bestResult.method.toUpperCase()}`);
            console.log('='.repeat(60));
            
            if (bestResult.data) {
                const projectInfo = extractProjectInfo(bestResult.data);
                if (projectInfo) {
                    console.log('\n📋 PROJECT INFORMATION:');
                    console.log('-'.repeat(40));
                    
                    for (const [key, value] of Object.entries(projectInfo)) {
                        if (value !== null && value !== undefined) {
                            let displayValue = value;
                            if (typeof value === 'string' && value.length > 100) {
                                displayValue = value.substring(0, 100) + '...';
                            } else if (typeof value === 'object') {
                                displayValue = `[${typeof value}]`;
                            }
                            console.log(`${key.padEnd(20)}: ${displayValue}`);
                        }
                    }
                }
                
                // Save the decoded data
                const outputFile = filename.replace('.json', '_decoded_remix.json');
                fs.writeFileSync(outputFile, JSON.stringify(bestResult.data, null, 2));
                console.log(`\n💾 Decoded data saved to: ${outputFile}`);
            }
        } else {
            console.log('\n❌ No decoder was successful');
        }
        
    } catch (error) {
        console.error(`❌ Error processing file: ${error.message}`);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { customDecode, testDecoders, extractProjectInfo };
