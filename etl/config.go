package etl

import (
	"fmt"

	pg "gitlab.realmond.dev/realmond-app1/backoffice-api/common/persistence/postgres"
)

// Config holds configuration options for the ETL subsystem.
//
// Each subsystem in backoffice-api follows the same pattern:
//   - A dedicated Config struct containing only the fields relevant to the
//     package.
//   - A NewConfig() constructor that returns a struct populated with sane
//     defaults so the application can start even without explicit user
//     configuration.
//
// For Task #2 we only need a placeholder for Postgres settings. Task #3 will
// introduce the real Postgres persistence package (common/persistence/postgres)
// and replace the placeholder with that implementation.

type Config struct {
	Postgres pg.Config `json:"postgres,omitempty" mapstructure:"postgres"`

	// SupportedEntityTypes lists the entity types the ETL subsystem is allowed to process. An empty slice means none are supported.
	SupportedEntityTypes []string `json:"supportedEntityTypes,omitempty" mapstructure:"supportedEntityTypes"`

	// DefaultBatchSize is the fallback batch size for ETL workflows when an entity-specific batch size is not provided.
	DefaultBatchSize int `json:"defaultBatchSize,omitempty" mapstructure:"defaultBatchSize"`

	// DefaultTenant specifies the tenant identifier used by SCD→Graph workflows when none is provided explicitly.
	DefaultTenant string `json:"defaultTenant,omitempty" mapstructure:"defaultTenant"`

	// EntityConfigs holds fine-grained configuration flags per entity type (e.g. projects, developers, buildings).
	EntityConfigs map[string]EntityConfig `json:"entityConfigs,omitempty" mapstructure:"entityConfigs"`

	// MaxConcurrentWorkflows limits the number of child workflows the ETL supervisor may spawn concurrently.
	MaxConcurrentWorkflows int `json:"maxConcurrentWorkflows,omitempty" mapstructure:"maxConcurrentWorkflows"`

	// SupervisorIntervalSeconds controls how often the multi-entity supervisor workflow runs.
	SupervisorIntervalSeconds int `json:"supervisorIntervalSeconds,omitempty" mapstructure:"supervisorIntervalSeconds"`
}

// NewConfig returns an ETL Config populated with default values.
func NewConfig() Config {
	return Config{
		Postgres:                  pg.NewConfig(),
		SupportedEntityTypes:      []string{"projects", "developers", "buildings"},
		DefaultBatchSize:          1000,
		DefaultTenant:             "default",
		MaxConcurrentWorkflows:    50,
		SupervisorIntervalSeconds: 300, // 5 minutes
		EntityConfigs: map[string]EntityConfig{
			"projects": {
				BatchSize:                  1000,
				ProcessingEnabled:          true,
				SupportedSources:           []string{}, // Empty → all sources
				GraphSyncEnabled:           true,
				SoftDeleteEnabled:          false,
				MaxConcurrentSources:       10,
				FastActivityTimeoutSeconds: 30,
				SlowActivityTimeoutSeconds: 600,
			},
			"developers": {
				BatchSize:                  500,
				ProcessingEnabled:          true,
				SupportedSources:           []string{},
				GraphSyncEnabled:           true,
				SoftDeleteEnabled:          false,
				MaxConcurrentSources:       5,
				FastActivityTimeoutSeconds: 30,
				SlowActivityTimeoutSeconds: 600,
			},
			"buildings": {
				BatchSize:                  1500,
				ProcessingEnabled:          true,
				SupportedSources:           []string{},
				GraphSyncEnabled:           true,
				SoftDeleteEnabled:          false,
				MaxConcurrentSources:       8,
				FastActivityTimeoutSeconds: 30,
				SlowActivityTimeoutSeconds: 600,
			},
		},
	}
}

// EntityConfig defines per-entity configuration knobs for the multi-entity ETL.
// Only structure definition is added for Phase 3 Task 3.1a – logic will follow in later tasks.
type EntityConfig struct {
	BatchSize int `json:"batchSize,omitempty" mapstructure:"batchSize"`

	ProcessingEnabled bool `json:"processingEnabled,omitempty" mapstructure:"processingEnabled"`

	SupportedSources []string `json:"supportedSources,omitempty" mapstructure:"supportedSources"`

	GraphSyncEnabled bool `json:"graphSyncEnabled,omitempty" mapstructure:"graphSyncEnabled"`

	SoftDeleteEnabled bool `json:"softDeleteEnabled,omitempty" mapstructure:"softDeleteEnabled"`

	MaxConcurrentSources int `json:"maxConcurrentSources,omitempty" mapstructure:"maxConcurrentSources"`

	FastActivityTimeoutSeconds int `json:"fastActivityTimeoutSeconds,omitempty" mapstructure:"fastActivityTimeoutSeconds"`

	SlowActivityTimeoutSeconds int `json:"slowActivityTimeoutSeconds,omitempty" mapstructure:"slowActivityTimeoutSeconds"`
}

// ValidateEntityType checks whether the provided entity type is declared in SupportedEntityTypes.
// Returns nil if supported, otherwise a descriptive error.
func (c *Config) ValidateEntityType(entityType string) error {
	for _, supported := range c.SupportedEntityTypes {
		if supported == entityType {
			return nil
		}
	}
	return fmt.Errorf("entity type '%s' is not in supported types: %v", entityType, c.SupportedEntityTypes)
}

// GetEntityConfig returns the EntityConfig for the given entity type or an error when absent.
func (c *Config) GetEntityConfig(entityType string) (EntityConfig, error) {
	cfg, ok := c.EntityConfigs[entityType]
	if !ok {
		return EntityConfig{}, fmt.Errorf("no configuration found for entity type: %s", entityType)
	}
	return cfg, nil
}

// IsEntityEnabled indicates whether ETL processing is enabled for the given entity type.
// If the entity type is not configured, the method returns false (disabled).
func (c *Config) IsEntityEnabled(entityType string) bool {
	cfg, err := c.GetEntityConfig(entityType)
	if err != nil {
		return false
	}
	return cfg.ProcessingEnabled
}

// IsSoftDeleteEnabled reports whether soft delete operations should be executed for the entity type.
// When the entity is not configured, the conservative default is true (perform deletes).
func (c *Config) IsSoftDeleteEnabled(entityType string) bool {
	cfg, err := c.GetEntityConfig(entityType)
	if err != nil {
		return true
	}
	return cfg.SoftDeleteEnabled
}
